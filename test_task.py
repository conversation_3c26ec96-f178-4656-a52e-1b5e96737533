"""
测试任务执行功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tool.screen_content import take_screenshot, screen_element, list_all_devices

def test_screen_capture_and_parse():
    """测试屏幕捕获和解析"""
    print("🚀 测试屏幕捕获和解析功能...")
    
    # 获取设备列表
    devices = list_all_devices()
    if not devices:
        print("❌ 未发现连接的设备")
        return False
    
    device = devices[0]
    print(f"📱 使用设备: {device}")
    
    # 截图
    print("📸 正在截图...")
    screenshot_path = take_screenshot.invoke({"device": device, "app_name": "test", "step": 1})

    if "failed" in screenshot_path.lower() or "error" in screenshot_path.lower():
        print(f"❌ 截图失败: {screenshot_path}")
        return False

    print(f"✅ 截图成功: {screenshot_path}")

    # 解析屏幕元素
    print("🔍 正在解析屏幕元素...")
    result = screen_element.invoke({"image_path": screenshot_path})
    
    if "error" in result:
        print(f"❌ 屏幕解析失败: {result['error']}")
        return False
    
    print("✅ 屏幕解析成功!")
    print(f"   标注图像路径: {result.get('labeled_image_path', 'N/A')}")
    print(f"   解析内容路径: {result.get('parsed_content_json_path', 'N/A')}")
    print(f"   处理时间: {result.get('elapsed_time', 'N/A')}秒")
    
    return True

if __name__ == "__main__":
    success = test_screen_capture_and_parse()
    
    if success:
        print("\n🎉 测试成功！屏幕捕获和解析功能正常工作")
        print("💡 现在可以尝试执行'打开微信'等任务了")
    else:
        print("\n❌ 测试失败，请检查配置")
