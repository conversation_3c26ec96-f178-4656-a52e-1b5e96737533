"""
简化的AppAgentX启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入demo模块
from demo import demo

if __name__ == "__main__":
    print("🚀 正在启动AppAgentX...")
    print("📝 注意：确保Mock后端服务已经在端口8000和8001上运行")
    print("🌐 启动Gradio界面...")
    
    try:
        # 使用最基本的启动参数
        demo.launch(
            server_name="127.0.0.1",
            server_port=7860,
            debug=True,
            show_error=True
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("🔧 尝试使用share=True...")
        try:
            demo.launch(share=True, debug=True)
        except Exception as e2:
            print(f"❌ 再次失败: {e2}")
            print("💡 请检查网络设置和防火墙配置")
