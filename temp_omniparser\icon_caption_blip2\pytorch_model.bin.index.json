{"metadata": {"total_size": 7489359872}, "weight_map": {"language_model.lm_head.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.embed_positions.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.embed_tokens.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.0.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.1.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.10.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.11.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.12.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.13.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.14.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.15.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.16.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.16.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.16.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.16.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.17.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.18.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.19.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.2.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.2.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.20.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.20.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.21.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.22.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.23.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.24.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.25.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.26.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.27.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.28.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.29.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.3.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.3.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.30.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.30.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.fc1.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.fc1.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.fc2.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.fc2.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.final_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.final_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.k_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.k_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.out_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.out_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.q_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.q_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.v_proj.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn.v_proj.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn_layer_norm.bias": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.31.self_attn_layer_norm.weight": "pytorch_model-00002-of-00002.bin", "language_model.model.decoder.layers.4.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.4.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.5.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.6.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.7.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.8.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.fc1.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.fc1.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.fc2.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.fc2.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.final_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.final_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.k_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.k_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.out_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.out_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.q_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.q_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.v_proj.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn.v_proj.weight": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn_layer_norm.bias": "pytorch_model-00001-of-00002.bin", "language_model.model.decoder.layers.9.self_attn_layer_norm.weight": "pytorch_model-00001-of-00002.bin", "language_projection.bias": "pytorch_model-00001-of-00002.bin", "language_projection.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.crossattention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.0.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.1.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.crossattention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.10.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.11.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.crossattention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.2.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.3.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.crossattention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.4.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.5.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.crossattention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.6.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.7.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.crossattention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.8.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.attention.key.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.attention.key.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.attention.query.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.attention.query.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.attention.value.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.attention.value.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.output.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.output.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.output.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.attention.output.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.intermediate_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.intermediate_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.output_query.LayerNorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.output_query.LayerNorm.weight": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.output_query.dense.bias": "pytorch_model-00001-of-00002.bin", "qformer.encoder.layer.9.output_query.dense.weight": "pytorch_model-00001-of-00002.bin", "qformer.layernorm.bias": "pytorch_model-00001-of-00002.bin", "qformer.layernorm.weight": "pytorch_model-00001-of-00002.bin", "query_tokens": "pytorch_model-00001-of-00002.bin", "vision_model.embeddings.class_embedding": "pytorch_model-00001-of-00002.bin", "vision_model.embeddings.patch_embedding.bias": "pytorch_model-00001-of-00002.bin", "vision_model.embeddings.patch_embedding.weight": "pytorch_model-00001-of-00002.bin", "vision_model.embeddings.position_embedding": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.0.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.1.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.10.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.11.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.12.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.13.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.14.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.15.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.16.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.17.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.18.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.19.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.2.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.20.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.21.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.22.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.23.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.24.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.25.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.26.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.27.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.28.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.29.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.3.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.30.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.31.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.32.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.33.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.34.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.35.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.36.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.37.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.38.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.4.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.5.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.6.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.7.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.8.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.layer_norm1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.layer_norm1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.layer_norm2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.layer_norm2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.mlp.fc1.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.mlp.fc1.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.mlp.fc2.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.mlp.fc2.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.self_attn.projection.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.self_attn.projection.weight": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.self_attn.qkv.bias": "pytorch_model-00001-of-00002.bin", "vision_model.encoder.layers.9.self_attn.qkv.weight": "pytorch_model-00001-of-00002.bin", "vision_model.post_layernorm.bias": "pytorch_model-00001-of-00002.bin", "vision_model.post_layernorm.weight": "pytorch_model-00001-of-00002.bin"}}