train_args:
  agnostic_nms: false
  amp: true
  augment: false
  auto_augment: randaugment
  batch: 64
  box: 7.5
  cache: false
  cfg: null
  classes: null
  close_mosaic: 10
  cls: 0.5
  conf: null
  copy_paste: 0.0
  cos_lr: false
  crop_fraction: 1.0
  degrees: 0.0
  deterministic: true
  device:
  - 0
  - 1
  - 2
  - 3
  dfl: 1.5
  dnn: false
  dropout: 0.0
  dynamic: false
  embed: null
  epochs: 20
  erasing: 0.4
  exist_ok: false
  fliplr: 0.5
  flipud: 0.0
  format: torchscript
  fraction: 1.0
  freeze: null
  half: false
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  imgsz: 1280
  int8: false
  iou: 0.7
  keras: false
  kobj: 1.0
  label_smoothing: 0.0
  line_width: null
  lr0: 0.01
  lrf: 0.01
  mask_ratio: 4
  max_det: 300
  mixup: 0.0
  mode: train
  model: yolov8n.pt
  momentum: 0.937
  mosaic: 0.0
  multi_scale: false
  nbs: 64
  nms: false
  opset: null
  optimize: false
  optimizer: auto
  overlap_mask: true
  patience: 100
  perspective: 0.0
  plots: true
  pose: 12.0
  pretrained: true
  profile: false
  project: null
  rect: false
  resume: false
  retina_masks: false
  save: true
  save_conf: false
  save_crop: false
  save_frames: false
  save_hybrid: false
  save_json: false
  save_period: -1
  save_txt: false
  scale: 0.5
  seed: 0
  shear: 0.0
  show: false
  show_boxes: true
  show_conf: true
  show_labels: true
  simplify: false
  single_cls: false
  source: null
  split: val
  stream_buffer: false
  task: detect
  time: null
  tracker: botsort.yaml
  translate: 0.1
  val: true
  verbose: true
  vid_stride: 1
  visualize: false
  warmup_bias_lr: 0.0
  warmup_epochs: 3.0
  warmup_momentum: 0.8
  weight_decay: 0.0005
  workers: 8
  workspace: 4
