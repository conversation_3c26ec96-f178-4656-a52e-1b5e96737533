"""
简单的Mock后端服务，用于在没有GPU的情况下启动AppAgentX
"""
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
import json
import base64
from PIL import Image
import io
import threading
import time

# 创建两个FastAPI应用
omni_app = FastAPI(title="Mock OmniParser Service")
embedding_app = FastAPI(title="Mock Image Embedding Service")

# Mock OmniParser服务 (端口8000)
@omni_app.post("/process_image/")
async def process_image(file: UploadFile = File(...)):
    """
    Mock图像解析服务 - 返回简单的模拟数据
    """
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 返回模拟的解析结果
        mock_result = {
            "status": "success",
            "message": "Image processed successfully (Mock Mode)",
            "data": {
                "elements": [
                    {
                        "id": "mock_element_1",
                        "type": "button",
                        "text": "Mock Button",
                        "bbox": [100, 100, 200, 150],
                        "confidence": 0.9
                    },
                    {
                        "id": "mock_element_2", 
                        "type": "text",
                        "text": "Mock Text",
                        "bbox": [50, 200, 300, 250],
                        "confidence": 0.8
                    }
                ],
                "image_size": list(image.size),
                "processed_time": time.time()
            }
        }
        
        return JSONResponse(content=mock_result)
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Mock processing failed: {str(e)}"
            }
        )

@omni_app.get("/")
async def omni_root():
    return {"message": "Mock OmniParser Service is running", "mode": "CPU_MOCK"}

# Mock图像嵌入服务 (端口8001)
@embedding_app.get("/available_models")
async def available_models():
    return {
        "models": ["mock_model_v1"],
        "current_model": "mock_model_v1",
        "mode": "CPU_MOCK"
    }

@embedding_app.post("/set_model")
async def set_model(model_data: dict):
    return {
        "status": "success",
        "message": f"Mock model set to: {model_data.get('model', 'unknown')}",
        "mode": "CPU_MOCK"
    }

@embedding_app.post("/extract_single/")
async def extract_single(file: UploadFile = File(...)):
    """
    Mock单图像特征提取
    """
    try:
        # 读取图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 返回模拟的特征向量
        mock_features = [0.1] * 512  # 512维的模拟特征向量
        
        return JSONResponse(content={
            "status": "success",
            "features": mock_features,
            "image_size": list(image.size),
            "model": "mock_model_v1",
            "mode": "CPU_MOCK"
        })
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error", 
                "message": f"Mock feature extraction failed: {str(e)}"
            }
        )

@embedding_app.post("/extract_batch/")
async def extract_batch(files: list[UploadFile] = File(...)):
    """
    Mock批量图像特征提取
    """
    results = []
    for i, file in enumerate(files):
        try:
            image_data = await file.read()
            image = Image.open(io.BytesIO(image_data))
            mock_features = [0.1 + i * 0.01] * 512  # 每个图像略有不同的特征
            
            results.append({
                "filename": file.filename,
                "features": mock_features,
                "image_size": list(image.size),
                "status": "success"
            })
        except Exception as e:
            results.append({
                "filename": file.filename,
                "status": "error",
                "message": str(e)
            })
    
    return JSONResponse(content={
        "status": "success",
        "results": results,
        "total_processed": len(results),
        "mode": "CPU_MOCK"
    })

@embedding_app.get("/model_info")
async def model_info():
    return {
        "model_name": "mock_model_v1",
        "feature_dim": 512,
        "device": "cpu",
        "mode": "CPU_MOCK",
        "status": "ready"
    }

@embedding_app.get("/benchmark/")
async def benchmark():
    return {
        "avg_processing_time": 0.1,
        "throughput": "10 images/sec",
        "device": "cpu",
        "mode": "CPU_MOCK"
    }

@embedding_app.get("/")
async def embedding_root():
    return {"message": "Mock Image Embedding Service is running", "mode": "CPU_MOCK"}

def run_omni_service():
    """运行OmniParser服务"""
    uvicorn.run(omni_app, host="127.0.0.1", port=8000, log_level="info")

def run_embedding_service():
    """运行图像嵌入服务"""
    uvicorn.run(embedding_app, host="127.0.0.1", port=8001, log_level="info")

if __name__ == "__main__":
    print("🚀 启动Mock后端服务...")
    print("📝 注意：这是CPU模式的Mock服务，用于演示和测试")
    print("🔧 OmniParser服务将在端口8000启动")
    print("🔧 图像嵌入服务将在端口8001启动")
    
    # 在单独的线程中启动两个服务
    omni_thread = threading.Thread(target=run_omni_service, daemon=True)
    embedding_thread = threading.Thread(target=run_embedding_service, daemon=True)
    
    omni_thread.start()
    embedding_thread.start()
    
    print("✅ Mock后端服务已启动")
    print("🌐 OmniParser: http://127.0.0.1:8000")
    print("🌐 Image Embedding: http://127.0.0.1:8001")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在关闭Mock后端服务...")
