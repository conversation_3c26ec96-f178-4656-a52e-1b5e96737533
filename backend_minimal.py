"""
最小化的后端服务，使用原本的技术栈但不依赖模型权重
"""
from fastapi import FastAPI, File, UploadFile
from fastapi.responses import JSONResponse
import uvicorn
import json
from PIL import Image
import io
import threading
import time
import numpy as np

# 创建两个FastAPI应用
omni_app = FastAPI(title="OmniParser Service")
embedding_app = FastAPI(title="Image Embedding Service")

print("🚀 启动最小化后端服务...")
print("📝 使用CPU模式，不依赖复杂模型")

# OmniParser服务 (端口8000)
@omni_app.post("/process_image/")
async def process_image(file: UploadFile = File(...)):
    """
    图像解析服务 - 基于图像分析的简单实现
    """
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        width, height = image.size
        
        # 基于图像尺寸和内容的简单元素检测
        elements = []
        
        # 模拟检测按钮（通常在屏幕下方）
        if height > 200:
            elements.append({
                "id": "button_1",
                "type": "button",
                "text": "Button",
                "bbox": [width//4, height*3//4, width*3//4, height*7//8],
                "confidence": 0.8
            })
        
        # 模拟检测文本区域（通常在屏幕中上部）
        if height > 100:
            elements.append({
                "id": "text_1",
                "type": "text",
                "text": "Text Content",
                "bbox": [width//10, height//4, width*9//10, height//2],
                "confidence": 0.7
            })
        
        # 模拟检测输入框（通常在屏幕中部）
        if width > 200 and height > 150:
            elements.append({
                "id": "input_1",
                "type": "input",
                "text": "Input Field",
                "bbox": [width//6, height//2, width*5//6, height*5//8],
                "confidence": 0.6
            })
        
        # 转换为前端期望的格式
        parsed_content = []
        for element in elements:
            parsed_content.append({
                "id": element["id"],
                "type": element["type"],
                "text": element["text"],
                "bbox": element["bbox"],
                "confidence": element["confidence"]
            })

        # 生成标注图像的base64数据（简化版）
        import base64
        from io import BytesIO

        # 创建一个简单的标注图像
        labeled_image = image.copy()
        # 这里可以添加实际的标注逻辑，现在先返回原图

        # 转换为base64
        buffered = BytesIO()
        labeled_image.save(buffered, format="PNG")
        labeled_image_base64 = base64.b64encode(buffered.getvalue()).decode()

        result = {
            "status": "success",
            "message": "Image processed successfully",
            "parsed_content": parsed_content,
            "labeled_image": labeled_image_base64,
            "e_time": 0.1,
            "data": {
                "elements": elements,
                "image_size": [width, height],
                "processed_time": time.time()
            }
        }
        
        return JSONResponse(content=result)
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Processing failed: {str(e)}"
            }
        )

@omni_app.get("/")
async def omni_root():
    return {
        "message": "OmniParser Service is running", 
        "mode": "minimal",
        "device": "cpu"
    }

# 图像嵌入服务 (端口8001)
@embedding_app.get("/available_models")
async def available_models():
    return {
        "models": ["minimal_extractor"],
        "current_model": "minimal_extractor",
        "device": "cpu"
    }

@embedding_app.post("/set_model")
async def set_model(model_data: dict):
    return {
        "status": "success",
        "message": f"Model set to: {model_data.get('model', 'minimal_extractor')}",
        "device": "cpu"
    }

@embedding_app.post("/extract_single/")
async def extract_single(file: UploadFile = File(...)):
    """
    单图像特征提取 - 基于图像统计信息
    """
    try:
        # 读取图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为numpy数组
        img_array = np.array(image)
        
        # 计算基本统计特征
        features = []
        
        # 尺寸特征
        width, height = image.size
        features.extend([width/1000.0, height/1000.0, (width*height)/1000000.0])
        
        # 颜色特征
        if len(img_array.shape) == 3:
            # RGB图像
            for channel in range(3):
                channel_data = img_array[:, :, channel]
                features.extend([
                    np.mean(channel_data)/255.0,
                    np.std(channel_data)/255.0,
                    np.min(channel_data)/255.0,
                    np.max(channel_data)/255.0
                ])
        else:
            # 灰度图像
            features.extend([
                np.mean(img_array)/255.0,
                np.std(img_array)/255.0,
                np.min(img_array)/255.0,
                np.max(img_array)/255.0
            ] * 3)  # 重复3次模拟RGB
        
        # 纹理特征（简化版）
        if len(img_array.shape) >= 2:
            # 计算梯度
            grad_x = np.abs(np.diff(img_array, axis=1)).mean()
            grad_y = np.abs(np.diff(img_array, axis=0)).mean()
            features.extend([grad_x/255.0, grad_y/255.0])
        
        # 填充到512维
        while len(features) < 512:
            features.append(np.random.random() * 0.1)
        
        features = features[:512]  # 截断到512维
        
        return JSONResponse(content={
            "status": "success",
            "features": features,
            "image_size": list(image.size),
            "model": "minimal_extractor",
            "device": "cpu"
        })
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error", 
                "message": f"Feature extraction failed: {str(e)}"
            }
        )

@embedding_app.post("/extract_batch/")
async def extract_batch(files: list[UploadFile] = File(...)):
    """
    批量图像特征提取
    """
    results = []
    for i, file in enumerate(files):
        try:
            # 重用单图像提取逻辑
            single_result = await extract_single(file)
            result_data = json.loads(single_result.body)
            
            results.append({
                "filename": file.filename,
                "features": result_data.get("features", []),
                "image_size": result_data.get("image_size", [0, 0]),
                "status": "success"
            })
        except Exception as e:
            results.append({
                "filename": file.filename,
                "status": "error",
                "message": str(e)
            })
    
    return JSONResponse(content={
        "status": "success",
        "results": results,
        "total_processed": len(results),
        "device": "cpu"
    })

@embedding_app.get("/model_info")
async def model_info():
    return {
        "model_name": "minimal_extractor",
        "feature_dim": 512,
        "device": "cpu",
        "status": "ready"
    }

@embedding_app.get("/benchmark/")
async def benchmark():
    return {
        "avg_processing_time": 0.05,
        "throughput": "20 images/sec",
        "device": "cpu",
        "mode": "minimal"
    }

@embedding_app.get("/")
async def embedding_root():
    return {
        "message": "Image Embedding Service is running", 
        "mode": "minimal",
        "device": "cpu"
    }

def run_omni_service():
    """运行OmniParser服务"""
    print("🔧 启动OmniParser服务 (端口8000)...")
    uvicorn.run(omni_app, host="127.0.0.1", port=8000, log_level="info")

def run_embedding_service():
    """运行图像嵌入服务"""
    print("🔧 启动图像嵌入服务 (端口8001)...")
    uvicorn.run(embedding_app, host="127.0.0.1", port=8001, log_level="info")

if __name__ == "__main__":
    print("🌐 OmniParser: http://127.0.0.1:8000")
    print("🌐 Image Embedding: http://127.0.0.1:8001")
    
    # 在单独的线程中启动两个服务
    omni_thread = threading.Thread(target=run_omni_service, daemon=True)
    embedding_thread = threading.Thread(target=run_embedding_service, daemon=True)
    
    omni_thread.start()
    embedding_thread.start()
    
    print("✅ 最小化后端服务已启动")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在关闭后端服务...")
