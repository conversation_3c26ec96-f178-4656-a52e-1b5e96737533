backbone:
- - -1
  - 1
  - Conv
  - - 64
    - 3
    - 2
- - -1
  - 1
  - Conv
  - - 128
    - 3
    - 2
- - -1
  - 3
  - C2f
  - - 128
    - true
- - -1
  - 1
  - Conv
  - - 256
    - 3
    - 2
- - -1
  - 6
  - C2f
  - - 256
    - true
- - -1
  - 1
  - Conv
  - - 512
    - 3
    - 2
- - -1
  - 6
  - C2f
  - - 512
    - true
- - -1
  - 1
  - Conv
  - - 1024
    - 3
    - 2
- - -1
  - 3
  - C2f
  - - 1024
    - true
- - -1
  - 1
  - SPPF
  - - 1024
    - 5
ch: 3
depth_multiple: 0.33
head:
- - -1
  - 1
  - nn.Upsample
  - - None
    - 2
    - nearest
- - - -1
    - 6
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C2f
  - - 512
- - -1
  - 1
  - nn.Upsample
  - - None
    - 2
    - nearest
- - - -1
    - 4
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C2f
  - - 256
- - -1
  - 1
  - Conv
  - - 256
    - 3
    - 2
- - - -1
    - 12
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C2f
  - - 512
- - -1
  - 1
  - Conv
  - - 512
    - 3
    - 2
- - - -1
    - 9
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C2f
  - - 1024
- - - 15
    - 18
    - 21
  - 1
  - Detect
  - - nc
nc: 1
scale: ''
width_multiple: 0.25
yaml_file: weights/icon_detect_v1_5/model.yaml
