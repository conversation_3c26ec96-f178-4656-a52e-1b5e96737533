"""
测试修复后的功能
"""
import requests
import json
from PIL import Image
import io
import base64

def test_backend_services():
    """测试后端服务"""
    print("🔧 测试后端服务...")
    
    # 测试OmniParser服务
    try:
        response = requests.get("http://127.0.0.1:8000/")
        if response.status_code == 200:
            print("✅ OmniParser服务正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ OmniParser服务异常: {response.status_code}")
    except Exception as e:
        print(f"❌ OmniParser服务连接失败: {e}")
    
    # 测试图像嵌入服务
    try:
        response = requests.get("http://127.0.0.1:8001/")
        if response.status_code == 200:
            print("✅ 图像嵌入服务正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 图像嵌入服务异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 图像嵌入服务连接失败: {e}")

def test_image_processing():
    """测试图像处理功能"""
    print("\n🖼️ 测试图像处理功能...")
    
    # 创建一个测试图像
    test_image = Image.new('RGB', (800, 600), color='white')
    
    # 转换为字节流
    img_byte_arr = io.BytesIO()
    test_image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    
    try:
        # 测试图像处理API
        files = {'file': ('test.png', img_byte_arr, 'image/png')}
        response = requests.post("http://127.0.0.1:8000/process_image/", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 图像处理成功")
            print(f"   状态: {data.get('status')}")
            print(f"   元素数量: {len(data.get('parsed_content', []))}")
            print(f"   是否有标注图像: {'labeled_image' in data}")
        else:
            print(f"❌ 图像处理失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 图像处理测试失败: {e}")

def test_neo4j_connection():
    """测试Neo4j连接"""
    print("\n🗄️ 测试Neo4j连接...")
    
    try:
        from data.graph_db import Neo4jDatabase
        import config
        
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        if db.connected:
            print("✅ Neo4j连接成功")
        else:
            print("⚠️ Neo4j连接失败，将使用本地模式")
    except Exception as e:
        print(f"❌ Neo4j测试失败: {e}")

def test_adb_connection():
    """测试ADB连接"""
    print("\n📱 测试ADB连接...")
    
    try:
        from tool.screen_content import list_all_devices
        devices = list_all_devices()
        
        if devices:
            print(f"✅ 发现 {len(devices)} 个设备:")
            for device in devices:
                print(f"   - {device}")
        else:
            print("⚠️ 未发现连接的设备")
    except Exception as e:
        print(f"❌ ADB测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试AppAgentX修复...")
    
    test_backend_services()
    test_image_processing()
    test_neo4j_connection()
    test_adb_connection()
    
    print("\n✅ 测试完成！")
