version: '3'

services:
  image-embedding:
    build:
      context: ./ImageEmbedding
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    volumes:
      - ./ImageEmbedding:/app
    restart: unless-stopped

  omni-parser:
    build:
      context: ./OmniParser
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./OmniParser:/app
      - ./OmniParser/weights:/app/weights
    restart: unless-stopped
